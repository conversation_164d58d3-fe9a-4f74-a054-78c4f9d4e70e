# InfixEdu Prisma Schema Collection

This repository contains a comprehensive Prisma schema collection for the InfixEdu School Management System v9.0.3, broken down into logical modules for better organization and maintainability.

## 📁 Schema Files Overview

### Core Schema Files

1. **`schema.prisma`** - Main schema file with datasource and generator configuration
2. **`user.prisma`** - User authentication and authorization models
3. **`academic.prisma`** - Academic structure (classes, sections, subjects, sessions)
4. **`student.prisma`** - Student management and records
5. **`staff.prisma`** - Staff management and HR functionality
6. **`fees.prisma`** - Fees management and payment processing
7. **`examination.prisma`** - Examination and assessment system
8. **`library.prisma`** - Library management system
9. **`inventory.prisma`** - Inventory and asset management
10. **`communication.prisma`** - Messaging, notifications, and events
11. **`system.prisma`** - System configuration and settings

## 🚀 Getting Started

### Prerequisites

- Node.js (v14 or higher)
- MySQL database
- Prisma CLI (`npm install -g prisma`)

### Installation

1. **Clone or download the schema files**
2. **Set up your environment variables**

Create a `.env` file in your project root:

```env
DATABASE_URL="mysql://username:password@localhost:3306/infixedu_db"
```

3. **Initialize Prisma (if starting fresh)**

```bash
npm init -y
npm install prisma @prisma/client
npx prisma init
```

4. **Combine schema files**

Since Prisma doesn't natively support multiple schema files, you have several options:

#### Option A: Manual Combination
Copy all model definitions from the individual `.prisma` files into your main `schema.prisma` file.

#### Option B: Build Script
Create a build script to combine files:

```javascript
// build-schema.js
const fs = require('fs');
const path = require('path');

const schemaFiles = [
  'user.prisma',
  'academic.prisma', 
  'student.prisma',
  'staff.prisma',
  'fees.prisma',
  'examination.prisma',
  'library.prisma',
  'inventory.prisma',
  'communication.prisma',
  'system.prisma'
];

let combinedSchema = `
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

`;

schemaFiles.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  // Remove generator and datasource blocks from individual files
  const modelContent = content.replace(/generator[\s\S]*?}\s*/g, '')
                             .replace(/datasource[\s\S]*?}\s*/g, '')
                             .replace(/\/\/.*$/gm, '') // Remove comments
                             .trim();
  if (modelContent) {
    combinedSchema += modelContent + '\n\n';
  }
});

fs.writeFileSync('schema.prisma', combinedSchema);
console.log('Schema files combined successfully!');
```

Run the build script:
```bash
node build-schema.js
```

### Database Setup

1. **Generate Prisma Client**
```bash
npx prisma generate
```

2. **Run migrations**
```bash
npx prisma db push
```

Or create and run migrations:
```bash
npx prisma migrate dev --name init
```

3. **Seed the database (optional)**
```bash
npx prisma db seed
```

## 📊 Database Schema Overview

### Key Features

- **Multi-tenancy**: School-based data isolation with `school_id` foreign keys
- **Role-based Access Control**: Comprehensive user roles and permissions
- **Academic Management**: Complete academic year, class, section, and subject management
- **Student Lifecycle**: From admission to graduation tracking
- **Staff Management**: HR, payroll, attendance, and performance tracking
- **Financial Management**: Fees, payments, discounts, and financial reporting
- **Assessment System**: Exams, marks, grades, and result processing
- **Communication**: Messaging, notifications, events, and announcements
- **Resource Management**: Library and inventory systems
- **System Configuration**: Flexible settings and customization options

### Core Relationships

```
Schools (1:N) → Users, Students, Staff, Classes
Classes (1:N) → Sections, Subjects, Students
Students (1:N) → Attendance, Fees, Marks, Documents
Staff (1:N) → Attendance, Payroll, Leave Requests
Exams (1:N) → Marks, Schedules, Results
```

## 🔧 Usage Examples

### Basic Queries

```typescript
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Get all students in a school
const students = await prisma.smStudent.findMany({
  where: { schoolId: 1 },
  include: {
    parent: true,
    category: true,
    studentRecords: true
  }
})

// Get class schedule
const schedule = await prisma.smClassRoutine.findMany({
  where: { 
    classId: 1,
    sectionId: 1 
  },
  include: {
    // Add relations as needed
  }
})

// Process fee payment
const payment = await prisma.smFeesPayment.create({
  data: {
    feesAssignId: 1,
    studentId: 1,
    amount: 1000,
    paymentDate: new Date(),
    paymentMethod: 'cash',
    schoolId: 1
  }
})
```

### Advanced Queries

```typescript
// Get student performance report
const studentReport = await prisma.smStudent.findUnique({
  where: { id: 1 },
  include: {
    studentRecords: {
      include: {
        academicYear: true
      }
    },
    attendances: {
      where: {
        attendanceDate: {
          gte: new Date('2024-01-01'),
          lte: new Date('2024-12-31')
        }
      }
    },
    // Add exam marks, fees, etc.
  }
})

// Get school dashboard data
const dashboardData = await prisma.$transaction([
  prisma.smStudent.count({ where: { schoolId: 1, activeStatus: 1 } }),
  prisma.smStaff.count({ where: { schoolId: 1, activeStatus: 1 } }),
  prisma.smClass.count({ where: { schoolId: 1, activeStatus: 1 } }),
  // Add more aggregations as needed
])
```

## 🔒 Security Considerations

1. **Row Level Security**: Implement school-based filtering in all queries
2. **Input Validation**: Validate all inputs before database operations
3. **Authentication**: Verify user permissions before data access
4. **Audit Logging**: Track all data modifications with user information

## 📝 Migration Notes

- **From Laravel**: This schema is based on Laravel migrations from InfixEdu v9.0.3
- **Data Types**: MySQL-specific data types are used (adjust for other databases)
- **Indexes**: Add appropriate indexes for performance optimization
- **Constraints**: Review and add business logic constraints as needed

## 🤝 Contributing

1. Follow the existing naming conventions
2. Add appropriate relations and constraints
3. Update documentation for any schema changes
4. Test migrations thoroughly before deployment

## 📄 License

This schema collection is based on the InfixEdu School Management System and should be used in accordance with the original software's licensing terms.

## 🆘 Support

For issues related to:
- **Schema Structure**: Check the original migration files in `all_tables.md`
- **Prisma Usage**: Refer to [Prisma Documentation](https://www.prisma.io/docs)
- **InfixEdu System**: Contact the original software vendor

---

**Note**: This is a comprehensive schema conversion from Laravel migrations to Prisma. Always test thoroughly in a development environment before using in production.
