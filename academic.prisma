// Academic Structure Models
// Based on InfixEdu migration analysis

// Academic years
model SmAcademicYear {
  id           Int       @id @default(autoincrement())
  year         String?   @db.VarChar(191)
  title        String?   @db.VarChar(191)
  startingDate DateTime? @map("starting_date") @db.Date
  endingDate   DateTime? @map("ending_date") @db.Date
  activeStatus Int       @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?      @default(1) @map("created_by")
  updatedBy    Int?      @default(1) @map("updated_by")
  schoolId     Int       @default(1) @map("school_id")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // Relations
  school         SmSchool        @relation(fields: [schoolId], references: [id], onDelete: Cascade)
  sessions       SmSession[]
  shifts         Shift[]
  studentRecords StudentRecord[]

  @@map("sm_academic_years")
}

// Academic sessions
model SmSession {
  id           Int      @id @default(autoincrement())
  sessionName  String?  @map("session_name") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  academicId   Int      @map("academic_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  academicYear SmAcademicYear @relation(fields: [academicId], references: [id], onDelete: Cascade)

  @@map("sm_sessions")
}

// Shifts (new feature in v9.x)
model Shift {
  id           Int      @id @default(autoincrement())
  name         String   @db.VarChar(255)
  startTime    String   @map("start_time") @db.VarChar(10)
  endTime      String   @map("end_time") @db.VarChar(10)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  academicId   Int      @map("academic_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  academicYear SmAcademicYear @relation(fields: [academicId], references: [id], onDelete: Cascade)

  @@map("shifts")
}

// Classes
model SmClass {
  id           Int      @id @default(autoincrement())
  className    String?  @map("class_name") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  school           SmSchool                 @relation(fields: [schoolId], references: [id], onDelete: Cascade)
  sections         SmSection[]
  classSections    SmClassSection[]
  subjects         SmSubject[]
  classTeachers    SmAssignClassTeacher[]
  optionalSubjects SmClassOptionalSubject[]

  @@map("sm_classes")
}

// Sections
model SmSection {
  id           Int      @id @default(autoincrement())
  sectionName  String?  @map("section_name") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  classId      Int      @map("class_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  class         SmClass          @relation(fields: [classId], references: [id], onDelete: Cascade)
  classSections SmClassSection[]

  @@map("sm_sections")
}

// Class-Section relationships
model SmClassSection {
  id           Int      @id @default(autoincrement())
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  class   SmClass   @relation(fields: [classId], references: [id], onDelete: Cascade)
  section SmSection @relation(fields: [sectionId], references: [id], onDelete: Cascade)

  @@index([classId, sectionId])
  @@map("sm_class_sections")
}

// Subjects
model SmSubject {
  id           Int      @id @default(autoincrement())
  subjectName  String?  @map("subject_name") @db.VarChar(200)
  subjectCode  String?  @map("subject_code") @db.VarChar(200)
  subjectType  Int?     @default(1) @map("subject_type")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  classId      Int      @map("class_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  class                  SmClass                   @relation(fields: [classId], references: [id], onDelete: Cascade)
  assignedSubjects       SmAssignSubject[]
  optionalSubjectAssigns SmOptionalSubjectAssign[]

  @@map("sm_subjects")
}

// Subject assignments to teachers
model SmAssignSubject {
  id           Int      @id @default(autoincrement())
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  teacherId    Int      @map("teacher_id")
  subjectId    Int      @map("subject_id")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  subject SmSubject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@map("sm_assign_subjects")
}

// Optional subject assignments
model SmOptionalSubjectAssign {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  subjectId    Int      @map("subject_id")
  sessionId    Int      @map("session_id")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  subject SmSubject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@map("sm_optional_subject_assigns")
}

// Class optional subjects
model SmClassOptionalSubject {
  id        Int @id @default(autoincrement())
  classId   Int @map("class_id")
  subjectId Int @map("subject_id")
  schoolId  Int @default(1) @map("school_id")

  // Relations
  class SmClass @relation(fields: [classId], references: [id], onDelete: Cascade)

  @@map("sm_class_optional_subject")
}

// Class teachers assignment
model SmAssignClassTeacher {
  id           Int      @id @default(autoincrement())
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  teacherId    Int      @map("teacher_id")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  class SmClass @relation(fields: [classId], references: [id], onDelete: Cascade)

  @@map("sm_assign_class_teachers")
}

// Class teachers (legacy table)
model SmClassTeacher {
  id           Int      @id @default(autoincrement())
  teacherId    Int      @map("teacher_id")
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_class_teachers")
}

// Class rooms
model SmClassRoom {
  id           Int      @id @default(autoincrement())
  roomNo       String?  @map("room_no") @db.VarChar(200)
  capacity     Int?
  roomType     String?  @map("room_type") @db.VarChar(200)
  description  String?  @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_class_rooms")
}

// Class routines/timetables
model SmClassRoutine {
  id           Int      @id @default(autoincrement())
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  subjectId    Int      @map("subject_id")
  teacherId    Int      @map("teacher_id")
  classTimeId  Int      @map("class_time_id")
  dayOfWeek    String   @map("day_of_week") @db.VarChar(20)
  roomId       Int?     @map("room_id")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_class_routines")
}

// Class times
model SmClassTime {
  id           Int      @id @default(autoincrement())
  period       String?  @db.VarChar(200)
  startTime    String?  @map("start_time") @db.VarChar(200)
  endTime      String?  @map("end_time") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_class_times")
}

// Weekends configuration
model SmWeekend {
  id           Int      @id @default(autoincrement())
  weekendName  String?  @map("weekend_name") @db.VarChar(200)
  order        Int?
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_weekends")
}

// Student records
model SmStudent {
  id           Int      @id @default(autoincrement())
  firstName    String   @map("first_name") @db.VarChar(200)
  lastName     String   @map("last_name") @db.VarChar(200)
  email        String   @map("email") @db.VarChar(200)
  phone        String?  @map("phone") @db.VarChar(200)
  dob          DateTime @map("dob")
  gender       String?  @map("gender") @db.VarChar(10)
  address      String?  @map("address") @db.Text
  city         String?  @map("city") @db.VarChar(100)
  state        String?  @map("state") @db.VarChar(100)
  country      String?  @map("country") @db.VarChar(100)
  postalCode   String?  @map("postal_code") @db.VarChar(20)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_students")
}

// te