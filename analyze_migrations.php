<?php

/**
 * <PERSON>ript to analyze all migration files and extract table information
 */

$migrationDir = '.';
$allTables = [];
$tableModifications = [];

// Get all migration files
$migrationFiles = glob($migrationDir . '/*.php');
sort($migrationFiles);

echo "Found " . count($migrationFiles) . " migration files\n";

foreach ($migrationFiles as $file) {
    $filename = basename($file);
    echo "Processing: $filename\n";
    
    $content = file_get_contents($file);
    
    // Extract table creation
    if (preg_match_all('/Schema::create\([\'"]([^\'"]+)[\'"]/', $content, $matches)) {
        foreach ($matches[1] as $tableName) {
            if (!isset($allTables[$tableName])) {
                $allTables[$tableName] = [
                    'created_in' => $filename,
                    'columns' => [],
                    'indexes' => [],
                    'foreign_keys' => []
                ];
            }
        }
    }
    
    // Extract table modifications
    if (preg_match_all('/Schema::table\([\'"]([^\'"]+)[\'"]/', $content, $matches)) {
        foreach ($matches[1] as $tableName) {
            if (!isset($tableModifications[$tableName])) {
                $tableModifications[$tableName] = [];
            }
            $tableModifications[$tableName][] = $filename;
        }
    }
    
    // Extract column definitions (basic pattern)
    if (preg_match_all('/\$blueprint->(\w+)\([\'"]?([^\'"(),]+)[\'"]?[^;]*;/', $content, $matches)) {
        // This would need more sophisticated parsing for full column details
    }
}

// Output results
echo "\n=== TABLES CREATED ===\n";
foreach ($allTables as $tableName => $info) {
    echo "$tableName (created in: {$info['created_in']})\n";
}

echo "\n=== TABLES MODIFIED ===\n";
foreach ($tableModifications as $tableName => $files) {
    echo "$tableName modified in: " . implode(', ', $files) . "\n";
}

echo "\nTotal tables created: " . count($allTables) . "\n";
echo "Total tables modified: " . count($tableModifications) . "\n";
