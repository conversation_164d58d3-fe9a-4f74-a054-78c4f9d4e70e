// Communication and Events Models
// Based on InfixEdu migration analysis

// Notice boards
model SmNoticeBoard {
  id           Int      @id @default(autoincrement())
  noticeTitle  String?  @map("notice_title") @db.VarChar(200)
  noticeMessage String? @map("notice_message") @db.Text
  noticeDate   DateTime @map("notice_date") @db.Date
  publishOn    DateTime @map("publish_on") @db.Date
  loginUrl     String?  @map("login_url") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_notice_boards")
}

// Send messages
model SmSendMessage {
  id           Int      @id @default(autoincrement())
  messageTitle String?  @map("message_title") @db.Var<PERSON>har(200)
  messageDetails String? @map("message_details") @db.Text
  sendThrough  String?  @map("send_through") @db.VarChar(200) // email, sms, system
  sendTo       String?  @map("send_to") @db.VarChar(200) // individual, group, class
  roleId       Int?     @map("role_id")
  classId      Int?     @map("class_id")
  sectionId    Int?     @map("section_id")
  studentId    Int?     @map("student_id")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_send_messages")
}

// Events
model SmEvent {
  id           Int      @id @default(autoincrement())
  eventTitle   String?  @map("event_title") @db.VarChar(200)
  forWhom      String?  @map("for_whom") @db.VarChar(200) // teacher, student, parents, all
  roleIds      String?  @map("role_ids") @db.Text
  url          String?  @db.Text
  eventLocation String? @map("event_location") @db.VarChar(200)
  eventDes     String?  @map("event_des") @db.VarChar(500)
  fromDate     DateTime? @map("from_date") @db.Date
  toDate       DateTime? @map("to_date") @db.Date
  uploadImageFile String? @map("uplad_image_file") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_events")
}

// Holidays
model SmHoliday {
  id           Int      @id @default(autoincrement())
  holidayTitle String?  @map("holiday_title") @db.VarChar(200)
  fromDate     DateTime @map("from_date") @db.Date
  toDate       DateTime @map("to_date") @db.Date
  totalDays    Int?     @map("total_days")
  details      String?  @db.Text
  uploadImageFile String? @map("upload_image_file") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_holidays")
}

// Notifications
model SmNotification {
  id           Int      @id @default(autoincrement())
  userId       Int      @map("user_id")
  roleId       Int      @map("role_id")
  date         DateTime @db.Date
  message      String?  @db.Text
  url          String?  @db.VarChar(200)
  isRead       Int      @default(0) @map("is_read") @db.TinyInt
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_notifications")
}

// Notification settings
model SmNotificationSetting {
  id           Int      @id @default(autoincrement())
  notificationType String @map("notification_type") @db.VarChar(200)
  isEmail      Int      @default(1) @map("is_email") @db.TinyInt
  isSms        Int      @default(1) @map("is_sms") @db.TinyInt
  isSystem     Int      @default(1) @map("is_system") @db.TinyInt
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_notification_settings")
}

// Email/SMS logs
model SmEmailSmsLog {
  id           Int      @id @default(autoincrement())
  title        String?  @db.VarChar(200)
  description  String?  @db.Text
  sendThrough  String?  @map("send_through") @db.VarChar(200) // email, sms
  sendTo       String?  @map("send_to") @db.VarChar(200)
  message      String?  @db.Text
  sentDate     DateTime @map("sent_date") @db.Date
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_email_sms_logs")
}

// Email settings
model SmEmailSetting {
  id           Int      @id @default(autoincrement())
  emailEngine  String?  @map("email_engine") @db.VarChar(200)
  fromName     String?  @map("from_name") @db.VarChar(200)
  fromEmail    String?  @map("from_email") @db.VarChar(200)
  smtpUsername String?  @map("smtp_username") @db.VarChar(200)
  smtpPassword String?  @map("smtp_password") @db.VarChar(200)
  smtpServer   String?  @map("smtp_server") @db.VarChar(200)
  smtpPort     String?  @map("smtp_port") @db.VarChar(200)
  smtpSecurity String?  @map("smtp_security") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_email_settings")
}

// SMS gateways
model SmSmsGateway {
  id           Int      @id @default(autoincrement())
  gatewayName  String?  @map("gateway_name") @db.VarChar(200)
  clickatellUsername String? @map("clickatell_username") @db.VarChar(200)
  clickatellPassword String? @map("clickatell_password") @db.VarChar(200)
  clickatellApiId String? @map("clickatell_api_id") @db.VarChar(200)
  twilioAccountSid String? @map("twilio_account_sid") @db.VarChar(200)
  twilioAuthenticationToken String? @map("twilio_authentication_token") @db.VarChar(200)
  twilioRegisteredNo String? @map("twilio_registered_no") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_sms_gateways")
}

// SMS templates
model SmsTemplate {
  id           Int      @id @default(autoincrement())
  type         String   @db.VarChar(200) // sms, email
  purpose      String   @db.VarChar(200)
  subject      String?  @db.VarChar(200)
  body         String   @db.Text
  module       String?  @db.VarChar(200)
  variable     String?  @db.Text
  status       Int      @default(1) @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sms_templates")
}

// Custom SMS settings
model CustomSmsSetting {
  id           Int      @id @default(autoincrement())
  gatewayName  String   @map("gateway_name") @db.VarChar(200)
  apiUrl       String?  @map("api_url") @db.VarChar(500)
  username     String?  @db.VarChar(200)
  password     String?  @db.VarChar(200)
  apiKey       String?  @map("api_key") @db.VarChar(200)
  senderId     String?  @map("sender_id") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("custom_sms_settings")
}

// Complaints
model SmComplaint {
  id           Int      @id @default(autoincrement())
  complaintBy  String?  @map("complaint_by") @db.VarChar(200)
  complaintType String? @map("complaint_type") @db.VarChar(200)
  complaintSource String? @map("complaint_source") @db.VarChar(200)
  phone        String?  @db.VarChar(200)
  date         DateTime @db.Date
  description  String?  @db.Text
  actionTaken  String?  @map("action_taken") @db.Text
  assigned     String?  @db.VarChar(200)
  file         String?  @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_complaints")
}

// Postal receives
model SmPostalReceive {
  id           Int      @id @default(autoincrement())
  fromTitle    String?  @map("from_title") @db.VarChar(200)
  referenceNo  String?  @map("reference_no") @db.VarChar(200)
  toTitle      String?  @map("to_title") @db.VarChar(200)
  date         DateTime @db.Date
  file         String?  @db.VarChar(200)
  message      String?  @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_postal_receives")
}

// Postal dispatches
model SmPostalDispatch {
  id           Int      @id @default(autoincrement())
  toTitle      String?  @map("to_title") @db.VarChar(200)
  referenceNo  String?  @map("reference_no") @db.VarChar(200)
  fromTitle    String?  @map("from_title") @db.VarChar(200)
  date         DateTime @db.Date
  file         String?  @db.VarChar(200)
  message      String?  @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_postal_dispatches")
}

// Phone call logs
model SmPhoneCallLog {
  id           Int      @id @default(autoincrement())
  name         String?  @db.VarChar(200)
  phone        String?  @db.VarChar(200)
  date         DateTime @db.Date
  description  String?  @db.Text
  nextFollowUpDate DateTime? @map("next_follow_up_date") @db.Date
  callDuration String?  @map("call_duration") @db.VarChar(200)
  callType     String?  @map("call_type") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_phone_call_logs")
}

// Visitors
model SmVisitor {
  id           Int      @id @default(autoincrement())
  name         String?  @db.VarChar(200)
  phone        String?  @db.VarChar(200)
  idNo         String?  @map("id_no") @db.VarChar(200)
  noOfPerson   Int?     @map("no_of_person")
  purpose      String?  @db.VarChar(200)
  date         DateTime @db.Date
  inTime       String?  @map("in_time") @db.VarChar(200)
  outTime      String?  @map("out_time") @db.VarChar(200)
  file         String?  @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_visitors")
}

// To-dos
model SmToDo {
  id           Int      @id @default(autoincrement())
  todoTitle    String?  @map("todo_title") @db.VarChar(200)
  date         DateTime @db.Date
  description  String?  @db.Text
  completeStatus String? @map("complete_status") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_to_dos")
}

// Admission queries
model SmAdmissionQuery {
  id               Int      @id @default(autoincrement())
  name             String?  @db.VarChar(255)
  phone            String?  @db.VarChar(255)
  email            String?  @db.VarChar(255)
  address          String?  @db.Text
  description      String?  @db.Text
  date             DateTime? @db.Date
  followUpDate     DateTime? @map("follow_up_date") @db.Date
  nextFollowUpDate DateTime? @map("next_follow_up_date") @db.Date
  assigned         String?  @db.VarChar(255)
  reference        Int?
  source           Int?
  noOfChild        Int?     @map("no_of_child")
  activeStatus     Int      @default(1) @map("active_status") @db.TinyInt
  createdBy        Int?     @default(1) @map("created_by")
  updatedBy        Int?     @default(1) @map("updated_by")
  schoolId         Int      @default(1) @map("school_id")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Relations
  followups        SmAdmissionQueryFollowup[]

  @@map("sm_admission_queries")
}

// Admission query followups
model SmAdmissionQueryFollowup {
  id               Int               @id @default(autoincrement())
  admissionQueryId Int               @map("admission_query_id")
  response         String?           @db.Text
  nextFollowUpDate DateTime?         @map("next_follow_up_date") @db.Date
  activeStatus     Int               @default(1) @map("active_status") @db.TinyInt
  createdBy        Int?              @default(1) @map("created_by")
  updatedBy        Int?              @default(1) @map("updated_by")
  schoolId         Int               @default(1) @map("school_id")
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")

  // Relations
  admissionQuery   SmAdmissionQuery  @relation(fields: [admissionQueryId], references: [id], onDelete: Cascade)

  @@map("sm_admission_query_followups")
}
