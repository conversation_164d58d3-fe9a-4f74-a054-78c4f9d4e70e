// Examination and Assessment Models
// Based on InfixEdu migration analysis

// Exam types
model SmExamType {
  id           Int      @id @default(autoincrement())
  title        String?  @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  parentId     Int?     @default(0) @map("parent_id")
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  exams        SmExam[]
  examSetups   SmExamSetup[]

  @@map("sm_exam_types")
}

// Exam types extension
model SmExamTypesExtension {
  id           Int      @id @default(autoincrement())
  examTypeId   Int      @map("exam_type_id")
  extensionData String? @map("extension_data") @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_exam_types_extension")
}

// Marks grades
model SmMarksGrade {
  id           Int      @id @default(autoincrement())
  gradeName    String?  @map("grade_name") @db.VarChar(200)
  gradePoint   Float?   @map("grade_point") @db.Float
  percentFrom  Float?   @map("percent_from") @db.Float
  percentUpto  Float?   @map("percent_upto") @db.Float
  description  String?  @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_marks_grades")
}

// Exams
model SmExam {
  id           Int       @id @default(autoincrement())
  examTypeId   Int       @map("exam_type_id")
  classId      Int       @map("class_id")
  sectionId    Int       @map("section_id")
  subjectId    Int       @map("subject_id")
  examMark     Int?      @map("exam_mark")
  passingMark  Int?      @map("passing_mark")
  activeStatus Int       @default(1) @map("active_status") @db.TinyInt
  parentId     Int?      @default(0) @map("parent_id")
  createdBy    Int?      @default(1) @map("created_by")
  updatedBy    Int?      @default(1) @map("updated_by")
  schoolId     Int       @default(1) @map("school_id")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // Relations
  examType     SmExamType @relation(fields: [examTypeId], references: [id], onDelete: Cascade)
  examSchedules SmExamSchedule[]
  markRegisters SmMarksRegister[]

  @@map("sm_exams")
}

// Exam schedules
model SmExamSchedule {
  id           Int      @id @default(autoincrement())
  examId       Int      @map("exam_id")
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  exam         SmExam   @relation(fields: [examId], references: [id], onDelete: Cascade)
  scheduleSubjects SmExamScheduleSubject[]

  @@map("sm_exam_schedules")
}

// Exam schedule subjects
model SmExamScheduleSubject {
  id             Int            @id @default(autoincrement())
  examScheduleId Int            @map("exam_schedule_id")
  subjectId      Int            @map("subject_id")
  date           DateTime?      @db.Date
  startTime      String?        @map("start_time") @db.VarChar(200)
  endTime        String?        @map("end_time") @db.VarChar(200)
  room           String?        @db.VarChar(200)
  fullMark       Int?           @map("full_mark")
  passMark       Int?           @map("pass_mark")
  activeStatus   Int            @default(1) @map("active_status") @db.TinyInt
  createdBy      Int?           @default(1) @map("created_by")
  updatedBy      Int?           @default(1) @map("updated_by")
  schoolId       Int            @default(1) @map("school_id")
  createdAt      DateTime       @default(now()) @map("created_at")
  updatedAt      DateTime       @updatedAt @map("updated_at")

  // Relations
  examSchedule   SmExamSchedule @relation(fields: [examScheduleId], references: [id], onDelete: Cascade)

  @@map("sm_exam_schedule_subjects")
}

// Marks registers
model SmMarksRegister {
  id           Int      @id @default(autoincrement())
  examId       Int      @map("exam_id")
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  subjectId    Int      @map("subject_id")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  exam         SmExam   @relation(fields: [examId], references: [id], onDelete: Cascade)
  markRegisterChildren SmMarksRegisterChild[]

  @@map("sm_marks_registers")
}

// Marks register children (individual student marks)
model SmMarksRegisterChild {
  id               Int             @id @default(autoincrement())
  marksRegisterId  Int             @map("marks_register_id")
  studentId        Int             @map("student_id")
  obtainedMarks    Float?          @map("obtained_marks") @db.Float
  teacherRemarks   String?         @map("teacher_remarks") @db.Text
  activeStatus     Int             @default(1) @map("active_status") @db.TinyInt
  createdBy        Int?            @default(1) @map("created_by")
  updatedBy        Int?            @default(1) @map("updated_by")
  schoolId         Int             @default(1) @map("school_id")
  createdAt        DateTime        @default(now()) @map("created_at")
  updatedAt        DateTime        @updatedAt @map("updated_at")

  // Relations
  marksRegister    SmMarksRegister @relation(fields: [marksRegisterId], references: [id], onDelete: Cascade)

  @@map("sm_marks_register_children")
}

// Exam marks registers (legacy)
model SmExamMarksRegister {
  id           Int      @id @default(autoincrement())
  examId       Int      @map("exam_id")
  studentId    Int      @map("student_id")
  subjectId    Int      @map("subject_id")
  obtainedMarks Float?  @map("obtained_marks") @db.Float
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_exam_marks_registers")
}

// Marks send SMS
model SmMarksSendSms {
  id           Int      @id @default(autoincrement())
  examId       Int      @map("exam_id")
  studentId    Int      @map("student_id")
  smsText      String?  @map("sms_text") @db.Text
  sentDate     DateTime @map("sent_date") @db.Date
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_marks_send_sms")
}

// Exam attendances
model SmExamAttendance {
  id           Int      @id @default(autoincrement())
  examId       Int      @map("exam_id")
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  subjectId    Int      @map("subject_id")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  attendanceChildren SmExamAttendanceChild[]

  @@map("sm_exam_attendances")
}

// Exam attendance children
model SmExamAttendanceChild {
  id               Int              @id @default(autoincrement())
  examAttendanceId Int              @map("exam_attendance_id")
  studentId        Int              @map("student_id")
  attendanceType   String?          @map("attendance_type") @db.VarChar(10)
  activeStatus     Int              @default(1) @map("active_status") @db.TinyInt
  createdBy        Int?             @default(1) @map("created_by")
  updatedBy        Int?             @default(1) @map("updated_by")
  schoolId         Int              @default(1) @map("school_id")
  createdAt        DateTime         @default(now()) @map("created_at")
  updatedAt        DateTime         @updatedAt @map("updated_at")

  // Relations
  examAttendance   SmExamAttendance @relation(fields: [examAttendanceId], references: [id], onDelete: Cascade)

  @@map("sm_exam_attendance_children")
}

// Seat plans
model SmSeatPlan {
  id           Int      @id @default(autoincrement())
  examId       Int      @map("exam_id")
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  subjectId    Int      @map("subject_id")
  date         DateTime @db.Date
  startTime    String?  @map("start_time") @db.VarChar(200)
  endTime      String?  @map("end_time") @db.VarChar(200)
  room         String?  @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  seatPlanChildren SmSeatPlanChild[]

  @@map("sm_seat_plans")
}

// Seat plan children
model SmSeatPlanChild {
  id           Int        @id @default(autoincrement())
  seatPlanId   Int        @map("seat_plan_id")
  studentId    Int        @map("student_id")
  rollNo       String?    @map("roll_no") @db.VarChar(200)
  seatNo       String?    @map("seat_no") @db.VarChar(200)
  activeStatus Int        @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?       @default(1) @map("created_by")
  updatedBy    Int?       @default(1) @map("updated_by")
  schoolId     Int        @default(1) @map("school_id")
  createdAt    DateTime   @default(now()) @map("created_at")
  updatedAt    DateTime   @updatedAt @map("updated_at")

  // Relations
  seatPlan     SmSeatPlan @relation(fields: [seatPlanId], references: [id], onDelete: Cascade)

  @@map("sm_seat_plan_children")
}

// Temporary merit lists
model SmTemporaryMeritlist {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  examId       Int      @map("exam_id")
  rollNo       String?  @map("roll_no") @db.VarChar(200)
  totalMarks   Float?   @map("total_marks") @db.Float
  gpa          Float?   @db.Float
  grade        String?  @db.VarChar(200)
  position     Int?
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_temporary_meritlists")
}

// Exam setups
model SmExamSetup {
  id           Int        @id @default(autoincrement())
  examTypeId   Int        @map("exam_type_id")
  classId      Int        @map("class_id")
  sectionId    Int        @map("section_id")
  examTitle    String?    @map("exam_title") @db.VarChar(200)
  examDate     DateTime?  @map("exam_date") @db.Date
  activeStatus Int        @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?       @default(1) @map("created_by")
  updatedBy    Int?       @default(1) @map("updated_by")
  schoolId     Int        @default(1) @map("school_id")
  createdAt    DateTime   @default(now()) @map("created_at")
  updatedAt    DateTime   @updatedAt @map("updated_at")

  // Relations
  examType     SmExamType @relation(fields: [examTypeId], references: [id], onDelete: Cascade)

  @@map("sm_exam_setups")
}

// Mark stores
model SmMarkStore {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  examTypeId   Int      @map("exam_type_id")
  subjectId    Int      @map("subject_id")
  obtainedMarks Float?  @map("obtained_marks") @db.Float
  totalMarks   Float?   @map("total_marks") @db.Float
  teacherRemarks String? @map("teacher_remarks") @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_mark_stores")
}

// Result stores
model SmResultStore {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  examTypeId   Int      @map("exam_type_id")
  totalMarks   Float?   @map("total_marks") @db.Float
  gpa          Float?   @db.Float
  grade        String?  @db.VarChar(200)
  position     Int?
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_result_stores")
}

// Exam settings
model SmExamSetting {
  id           Int      @id @default(autoincrement())
  examTypeId   Int      @map("exam_type_id")
  settingName  String   @map("setting_name") @db.VarChar(200)
  settingValue String?  @map("setting_value") @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_exam_settings")
}

// Exam signatures
model SmExamSignature {
  id           Int      @id @default(autoincrement())
  examTypeId   Int      @map("exam_type_id")
  signatureTitle String @map("signature_title") @db.VarChar(200)
  signatureImage String? @map("signature_image") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_exam_signatures")
}

// Exam merit positions
model ExamMeritPosition {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  examId       Int      @map("exam_id")
  totalMarks   Float?   @map("total_marks") @db.Float
  gpa          Float?   @db.Float
  grade        String?  @db.VarChar(255)
  position     Int?
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("exam_merit_positions")
}

// All exam wise positions
model AllExamWisePosition {
  id           Int      @id @default(autoincrement())
  classId      Int?     @map("class_id")
  sectionId    Int?     @map("section_id")
  totalMark    Float?   @map("total_mark") @db.Float
  position     Int?
  rollNo       Int?     @map("roll_no")
  admissionNo  Int?     @map("admission_no")
  gpa          Float?   @db.Float
  grade        String?  @db.VarChar(255)
  recordId     Int?     @map("record_id")
  schoolId     Int      @map("school_id")
  academicId   Int      @map("academic_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("all_exam_wise_positions")
}

// Exam step skips
model ExamStepSkip {
  id           Int      @id @default(autoincrement())
  examId       Int      @map("exam_id")
  stepName     String   @map("step_name") @db.VarChar(200)
  isSkipped    Int      @default(0) @map("is_skipped") @db.TinyInt
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("exam_step_skips")
}

// Custom result settings
model CustomResultSetting {
  id           Int      @id @default(autoincrement())
  templateName String   @map("template_name") @db.VarChar(200)
  templateData String?  @map("template_data") @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("custom_result_settings")
}

// Custom temporary results
model SmCustomTemporaryResult {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  examId       Int      @map("exam_id")
  resultData   String?  @map("result_data") @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_custom_temporary_results")
}
