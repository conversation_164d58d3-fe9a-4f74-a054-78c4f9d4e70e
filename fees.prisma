// Fees Management Models
// Based on InfixEdu migration analysis

// Fee groups
model SmFeesGroup {
  id           Int      @id @default(autoincrement())
  name         String?  @db.VarChar(200)
  description  String?  @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  feesTypes    SmFeesType[]
  feesMasters  SmFeesMaster[]

  @@map("sm_fees_groups")
}

// Fee types
model SmFeesType {
  id           Int         @id @default(autoincrement())
  name         String?     @db.VarChar(200)
  description  String?     @db.Text
  feesGroupId  Int         @map("fees_group_id")
  activeStatus Int         @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?        @default(1) @map("created_by")
  updatedBy    Int?        @default(1) @map("updated_by")
  schoolId     Int         @default(1) @map("school_id")
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")

  // Relations
  feesGroup    SmFeesGroup @relation(fields: [feesGroupId], references: [id], onDelete: Cascade)
  feesMasters  SmFeesMaster[]
  feesAssigns  SmFeesAssign[]

  @@map("sm_fees_types")
}

// Fee discounts
model SmFeesDiscount {
  id           Int      @id @default(autoincrement())
  name         String?  @db.VarChar(200)
  code         String?  @db.VarChar(200)
  type         String?  @db.VarChar(200) // percentage or fixed
  amount       Float?   @db.Float
  description  String?  @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  feesAssignDiscounts SmFeesAssignDiscount[]

  @@map("sm_fees_discounts")
}

// Fee masters
model SmFeesMaster {
  id           Int         @id @default(autoincrement())
  feesGroupId  Int         @map("fees_group_id")
  feesTypeId   Int         @map("fees_type_id")
  date         DateTime?   @db.Date
  amount       Float?      @db.Float
  activeStatus Int         @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?        @default(1) @map("created_by")
  updatedBy    Int?        @default(1) @map("updated_by")
  schoolId     Int         @default(1) @map("school_id")
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")

  // Relations
  feesGroup    SmFeesGroup @relation(fields: [feesGroupId], references: [id], onDelete: Cascade)
  feesType     SmFeesType  @relation(fields: [feesTypeId], references: [id], onDelete: Cascade)

  @@map("sm_fees_masters")
}

// Fee assignments to students
model SmFeesAssign {
  id           Int        @id @default(autoincrement())
  studentId    Int        @map("student_id")
  feesTypeId   Int        @map("fees_type_id")
  amount       Float?     @db.Float
  dueDate      DateTime?  @map("due_date") @db.Date
  activeStatus Int        @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?       @default(1) @map("created_by")
  updatedBy    Int?       @default(1) @map("updated_by")
  schoolId     Int        @default(1) @map("school_id")
  createdAt    DateTime   @default(now()) @map("created_at")
  updatedAt    DateTime   @updatedAt @map("updated_at")

  // Relations
  feesType     SmFeesType @relation(fields: [feesTypeId], references: [id], onDelete: Cascade)
  feesPayments SmFeesPayment[]
  assignDiscounts SmFeesAssignDiscount[]

  @@map("sm_fees_assigns")
}

// Fee assign discounts
model SmFeesAssignDiscount {
  id             Int            @id @default(autoincrement())
  feesAssignId   Int            @map("fees_assign_id")
  feesDiscountId Int            @map("fees_discount_id")
  appliedAmount  Float?         @map("applied_amount") @db.Float
  activeStatus   Int            @default(1) @map("active_status") @db.TinyInt
  createdBy      Int?           @default(1) @map("created_by")
  updatedBy      Int?           @default(1) @map("updated_by")
  schoolId       Int            @default(1) @map("school_id")
  createdAt      DateTime       @default(now()) @map("created_at")
  updatedAt      DateTime       @updatedAt @map("updated_at")

  // Relations
  feesAssign     SmFeesAssign   @relation(fields: [feesAssignId], references: [id], onDelete: Cascade)
  feesDiscount   SmFeesDiscount @relation(fields: [feesDiscountId], references: [id], onDelete: Cascade)

  @@map("sm_fees_assign_discounts")
}

// Fee payments
model SmFeesPayment {
  id             Int          @id @default(autoincrement())
  feesAssignId   Int          @map("fees_assign_id")
  studentId      Int          @map("student_id")
  amount         Float        @db.Float
  paymentDate    DateTime     @map("payment_date") @db.Date
  paymentMethod  String?      @map("payment_method") @db.VarChar(200)
  note           String?      @db.Text
  activeStatus   Int          @default(1) @map("active_status") @db.TinyInt
  createdBy      Int?         @default(1) @map("created_by")
  updatedBy      Int?         @default(1) @map("updated_by")
  schoolId       Int          @default(1) @map("school_id")
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @updatedAt @map("updated_at")

  // Relations
  feesAssign     SmFeesAssign @relation(fields: [feesAssignId], references: [id], onDelete: Cascade)

  @@map("sm_fees_payments")
}

// Fee carry forwards
model SmFeesCarryForward {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  feesTypeId   Int      @map("fees_type_id")
  previousAmount Float? @map("previous_amount") @db.Float
  carriedAmount Float?  @map("carried_amount") @db.Float
  balanceType  String?  @map("balance_type") @db.VarChar(255)
  dueDate      DateTime? @map("due_date")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_fees_carry_forwards")
}

// Direct fees installments
model DirectFeesInstallment {
  id           Int      @id @default(autoincrement())
  name         String   @db.VarChar(200)
  amount       Float    @db.Float
  dueDate      DateTime @map("due_date") @db.Date
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  installmentAssigns DirectFeesInstallmentAssign[]

  @@map("direct_fees_installments")
}

// Direct fees installment assignments
model DirectFeesInstallmentAssign {
  id             Int                   @id @default(autoincrement())
  installmentId  Int                   @map("installment_id")
  studentId      Int                   @map("student_id")
  classId        Int                   @map("class_id")
  sectionId      Int                   @map("section_id")
  activeStatus   Int                   @default(1) @map("active_status") @db.TinyInt
  schoolId       Int                   @default(1) @map("school_id")
  createdAt      DateTime              @default(now()) @map("created_at")
  updatedAt      DateTime              @updatedAt @map("updated_at")

  // Relations
  installment    DirectFeesInstallment @relation(fields: [installmentId], references: [id], onDelete: Cascade)

  @@map("direct_fees_installment_assigns")
}

// Direct fees installment child payments
model DireFeesInstallmentChildPayment {
  id             Int      @id @default(autoincrement())
  installmentId  Int      @map("installment_id")
  studentId      Int      @map("student_id")
  amount         Float    @db.Float
  paymentDate    DateTime @map("payment_date") @db.Date
  paymentMethod  String?  @map("payment_method") @db.VarChar(200)
  activeStatus   Int      @default(1) @map("active_status") @db.TinyInt
  schoolId       Int      @default(1) @map("school_id")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  @@map("dire_fees_installment_child_payments")
}

// Fees invoices
model FeesInvoice {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  invoiceNo    String   @map("invoice_no") @db.VarChar(200)
  invoiceDate  DateTime @map("invoice_date") @db.Date
  dueDate      DateTime @map("due_date") @db.Date
  totalAmount  Float    @map("total_amount") @db.Float
  paidAmount   Float    @default(0) @map("paid_amount") @db.Float
  status       String   @default("unpaid") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("fees_invoices")
}

// Direct fees settings
model DirectFeesSetting {
  id           Int      @id @default(autoincrement())
  settingName  String   @map("setting_name") @db.VarChar(200)
  settingValue String?  @map("setting_value") @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("direct_fees_settings")
}

// Direct fees reminders
model DirectFeesReminder {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  feesTypeId   Int      @map("fees_type_id")
  reminderDate DateTime @map("reminder_date") @db.Date
  reminderType String   @map("reminder_type") @db.VarChar(200) // email, sms
  message      String?  @db.Text
  status       String   @default("pending") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("direct_fees_reminders")
}

// Fees installment credits
model FeesInstallmentCredit {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  installmentId Int     @map("installment_id")
  creditAmount Float    @map("credit_amount") @db.Float
  creditDate   DateTime @map("credit_date") @db.Date
  reason       String?  @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("fees_installment_credits")
}

// Fees carry forward settings
model FeesCarryForwardSetting {
  id           Int      @id @default(autoincrement())
  settingName  String   @map("setting_name") @db.VarChar(200)
  settingValue String?  @map("setting_value") @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("fees_carry_forward_settings")
}

// Fees carry forward logs
model FeesCarryForwardLog {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  fromSession  String   @map("from_session") @db.VarChar(200)
  toSession    String   @map("to_session") @db.VarChar(200)
  amount       Float    @db.Float
  processDate  DateTime @map("process_date") @db.Date
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("fees_carry_forward_logs")
}

// Due fees login prevents
model DueFeesLoginPrevent {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  dueAmount    Float    @map("due_amount") @db.Float
  preventDate  DateTime @map("prevent_date") @db.Date
  isActive     Int      @default(1) @map("is_active") @db.TinyInt
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("due_fees_login_prevents")
}

// Payment gateway settings
model SmPaymentGatewaySetting {
  id           Int      @id @default(autoincrement())
  gatewayName  String   @map("gateway_name") @db.VarChar(200)
  gatewayUsername String? @map("gateway_username") @db.VarChar(200)
  gatewayPassword String? @map("gateway_password") @db.VarChar(200)
  gatewaySignature String? @map("gateway_signature") @db.VarChar(200)
  gatewayClientId String? @map("gateway_client_id") @db.VarChar(200)
  gatewayMode  String?  @map("gateway_mode") @db.VarChar(200) // sandbox or live
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_payment_gateway_settings")
}

// Payment methods
model SmPaymentMethod {
  id           Int      @id @default(autoincrement())
  method       String?  @db.VarChar(200)
  type         String?  @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_payment_methhods")
}

// Bank payment slips
model SmBankPaymentSlip {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  feesTypeId   Int      @map("fees_type_id")
  slipNo       String   @map("slip_no") @db.VarChar(200)
  amount       Float    @db.Float
  paymentDate  DateTime @map("payment_date") @db.Date
  bankName     String?  @map("bank_name") @db.VarChar(200)
  slipFile     String?  @map("slip_file") @db.VarChar(200)
  status       String   @default("pending") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_bank_payment_slips")
}

// Bank statements
model SmBankStatement {
  id           Int      @id @default(autoincrement())
  bankName     String   @map("bank_name") @db.VarChar(200)
  accountNo    String   @map("account_no") @db.VarChar(200)
  statementDate DateTime @map("statement_date") @db.Date
  openingBalance Float  @map("opening_balance") @db.Float
  closingBalance Float  @map("closing_balance") @db.Float
  totalCredit  Float    @map("total_credit") @db.Float
  totalDebit   Float    @map("total_debit") @db.Float
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_bank_statements")
}

// Amount transfers
model SmAmountTransfer {
  id           Int      @id @default(autoincrement())
  fromAccount  String   @map("from_account") @db.VarChar(200)
  toAccount    String   @map("to_account") @db.VarChar(200)
  amount       Float    @db.Float
  transferDate DateTime @map("transfer_date") @db.Date
  reason       String?  @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_amount_transfers")
}
