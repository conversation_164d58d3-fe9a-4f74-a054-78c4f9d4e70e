// Inventory Management Models
// Based on InfixEdu migration analysis

// Item categories
model SmItemCategory {
  id           Int      @id @default(autoincrement())
  categoryName String?  @map("category_name") @db.VarChar(200)
  categoryCode String?  @map("category_code") @db.VarChar(200)
  description  String?  @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  items        SmItem[]

  @@map("sm_item_categories")
}

// Items
model SmItem {
  id             Int             @id @default(autoincrement())
  itemName       String?         @map("item_name") @db.VarChar(200)
  itemCategoryId Int             @map("item_category_id")
  itemCode       String?         @map("item_code") @db.VarChar(200)
  description    String?         @db.Text
  activeStatus   Int             @default(1) @map("active_status") @db.TinyInt
  createdBy      Int?            @default(1) @map("created_by")
  updatedBy      Int?            @default(1) @map("updated_by")
  schoolId       Int             @default(1) @map("school_id")
  createdAt      DateTime        @default(now()) @map("created_at")
  updatedAt      DateTime        @updatedAt @map("updated_at")

  // Relations
  itemCategory   SmItemCategory  @relation(fields: [itemCategoryId], references: [id], onDelete: Cascade)
  itemReceiveChildren SmItemReceiveChild[]
  itemSellChildren SmItemSellChild[]
  itemIssues     SmItemIssue[]

  @@map("sm_items")
}

// Item stores
model SmItemStore {
  id           Int      @id @default(autoincrement())
  storeName    String?  @map("store_name") @db.VarChar(200)
  storeNo      String?  @map("store_no") @db.VarChar(200)
  description  String?  @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  itemReceives SmItemReceive[]
  itemSells    SmItemSell[]

  @@map("sm_item_stores")
}

// Suppliers
model SmSupplier {
  id              Int      @id @default(autoincrement())
  companyName     String?  @map("company_name") @db.VarChar(200)
  contactPerson   String?  @map("contact_person") @db.VarChar(200)
  phoneNo         String?  @map("phone_no") @db.VarChar(200)
  email           String?  @db.VarChar(200)
  address         String?  @db.Text
  contactPersonDesignation String? @map("contact_person_designation") @db.VarChar(200)
  description     String?  @db.Text
  activeStatus    Int      @default(1) @map("active_status") @db.TinyInt
  createdBy       Int?     @default(1) @map("created_by")
  updatedBy       Int?     @default(1) @map("updated_by")
  schoolId        Int      @default(1) @map("school_id")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  itemReceives    SmItemReceive[]

  @@map("sm_suppliers")
}

// Item receives (purchases)
model SmItemReceive {
  id             Int            @id @default(autoincrement())
  supplierId     Int            @map("supplier_id")
  storeId        Int            @map("store_id")
  receiveDate    DateTime       @map("receive_date") @db.Date
  referenceNo    String?        @map("reference_no") @db.VarChar(200)
  grandTotal     Float?         @map("grand_total") @db.Float
  totalQuantity  Float?         @map("total_quantity") @db.Float
  totalPaid      Float?         @map("total_paid") @db.Float
  totalDue       Float?         @map("total_due") @db.Float
  description    String?        @db.Text
  activeStatus   Int            @default(1) @map("active_status") @db.TinyInt
  createdBy      Int?           @default(1) @map("created_by")
  updatedBy      Int?           @default(1) @map("updated_by")
  schoolId       Int            @default(1) @map("school_id")
  createdAt      DateTime       @default(now()) @map("created_at")
  updatedAt      DateTime       @updatedAt @map("updated_at")

  // Relations
  supplier       SmSupplier     @relation(fields: [supplierId], references: [id], onDelete: Cascade)
  store          SmItemStore    @relation(fields: [storeId], references: [id], onDelete: Cascade)
  receiveChildren SmItemReceiveChild[]
  inventoryPayments SmInventoryPayment[]

  @@map("sm_item_receives")
}

// Item receive children (purchase details)
model SmItemReceiveChild {
  id             Int           @id @default(autoincrement())
  itemReceiveId  Int           @map("item_receive_id")
  itemId         Int           @map("item_id")
  quantity       Float?        @db.Float
  unitPrice      Float?        @map("unit_price") @db.Float
  subTotal       Float?        @map("sub_total") @db.Float
  description    String?       @db.Text
  activeStatus   Int           @default(1) @map("active_status") @db.TinyInt
  createdBy      Int?          @default(1) @map("created_by")
  updatedBy      Int?          @default(1) @map("updated_by")
  schoolId       Int           @default(1) @map("school_id")
  createdAt      DateTime      @default(now()) @map("created_at")
  updatedAt      DateTime      @updatedAt @map("updated_at")

  // Relations
  itemReceive    SmItemReceive @relation(fields: [itemReceiveId], references: [id], onDelete: Cascade)
  item           SmItem        @relation(fields: [itemId], references: [id], onDelete: Cascade)

  @@map("sm_item_receive_children")
}

// Item sells
model SmItemSell {
  id             Int            @id @default(autoincrement())
  storeId        Int            @map("store_id")
  sellDate       DateTime       @map("sell_date") @db.Date
  referenceNo    String?        @map("reference_no") @db.VarChar(200)
  grandTotal     Float?         @map("grand_total") @db.Float
  totalQuantity  Float?         @map("total_quantity") @db.Float
  totalPaid      Float?         @map("total_paid") @db.Float
  totalDue       Float?         @map("total_due") @db.Float
  description    String?        @db.Text
  activeStatus   Int            @default(1) @map("active_status") @db.TinyInt
  createdBy      Int?           @default(1) @map("created_by")
  updatedBy      Int?           @default(1) @map("updated_by")
  schoolId       Int            @default(1) @map("school_id")
  createdAt      DateTime       @default(now()) @map("created_at")
  updatedAt      DateTime       @updatedAt @map("updated_at")

  // Relations
  store          SmItemStore    @relation(fields: [storeId], references: [id], onDelete: Cascade)
  sellChildren   SmItemSellChild[]

  @@map("sm_item_sells")
}

// Item sell children (sale details)
model SmItemSellChild {
  id             Int         @id @default(autoincrement())
  itemSellId     Int         @map("item_sell_id")
  itemId         Int         @map("item_id")
  quantity       Float?      @db.Float
  unitPrice      Float?      @map("unit_price") @db.Float
  subTotal       Float?      @map("sub_total") @db.Float
  description    String?     @db.Text
  activeStatus   Int         @default(1) @map("active_status") @db.TinyInt
  createdBy      Int?        @default(1) @map("created_by")
  updatedBy      Int?        @default(1) @map("updated_by")
  schoolId       Int         @default(1) @map("school_id")
  createdAt      DateTime    @default(now()) @map("created_at")
  updatedAt      DateTime    @updatedAt @map("updated_at")

  // Relations
  itemSell       SmItemSell  @relation(fields: [itemSellId], references: [id], onDelete: Cascade)
  item           SmItem      @relation(fields: [itemId], references: [id], onDelete: Cascade)

  @@map("sm_item_sell_children")
}

// Item issues
model SmItemIssue {
  id             Int      @id @default(autoincrement())
  itemId         Int      @map("item_id")
  quantity       Float    @db.Float
  issueDate      DateTime @map("issue_date") @db.Date
  dueReturnDate  DateTime @map("due_return_date") @db.Date
  returnDate     DateTime? @map("return_date") @db.Date
  issueTo        String?  @map("issue_to") @db.VarChar(200)
  issueBy        String?  @map("issue_by") @db.VarChar(200)
  note           String?  @db.Text
  activeStatus   Int      @default(1) @map("active_status") @db.TinyInt
  createdBy      Int?     @default(1) @map("created_by")
  updatedBy      Int?     @default(1) @map("updated_by")
  schoolId       Int      @default(1) @map("school_id")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  item           SmItem   @relation(fields: [itemId], references: [id], onDelete: Cascade)

  @@map("sm_item_issues")
}

// Inventory payments
model SmInventoryPayment {
  id             Int           @id @default(autoincrement())
  itemReceiveId  Int           @map("item_receive_id")
  paymentDate    DateTime      @map("payment_date") @db.Date
  amount         Float         @db.Float
  paymentMethod  String?       @map("payment_method") @db.VarChar(200)
  note           String?       @db.Text
  activeStatus   Int           @default(1) @map("active_status") @db.TinyInt
  createdBy      Int?          @default(1) @map("created_by")
  updatedBy      Int?          @default(1) @map("updated_by")
  schoolId       Int           @default(1) @map("school_id")
  createdAt      DateTime      @default(now()) @map("created_at")
  updatedAt      DateTime      @updatedAt @map("updated_at")

  // Relations
  itemReceive    SmItemReceive @relation(fields: [itemReceiveId], references: [id], onDelete: Cascade)

  @@map("sm_inventory_payments")
}
