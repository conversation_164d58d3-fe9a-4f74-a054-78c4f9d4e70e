// Library Management Models
// Based on InfixEdu migration analysis

// Book categories
model SmBookCategory {
  id           Int      @id @default(autoincrement())
  categoryName String?  @map("category_name") @db.VarChar(200)
  categoryCode String?  @map("category_code") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  books        SmBook[]

  @@map("sm_book_categories")
}

// Books
model SmBook {
  id             Int             @id @default(autoincrement())
  bookTitle      String?         @map("book_title") @db.VarChar(200)
  bookCategoryId Int             @map("book_category_id")
  bookNo         String?         @map("book_no") @db.VarChar(200)
  isbn           String?         @db.VarChar(200)
  publisherName  String?         @map("publisher_name") @db.VarChar(200)
  authorName     String?         @map("author_name") @db.VarChar(200)
  subjectId      Int?            @map("subject_id")
  rackNo         String?         @map("rack_no") @db.VarChar(200)
  quantity       Int?
  bookPrice      Float?          @map("book_price") @db.Float
  details        String?         @db.Text
  postDate       DateTime?       @map("post_date") @db.Date
  activeStatus   Int             @default(1) @map("active_status") @db.TinyInt
  createdBy      Int?            @default(1) @map("created_by")
  updatedBy      Int?            @default(1) @map("updated_by")
  schoolId       Int             @default(1) @map("school_id")
  createdAt      DateTime        @default(now()) @map("created_at")
  updatedAt      DateTime        @updatedAt @map("updated_at")

  // Relations
  bookCategory   SmBookCategory  @relation(fields: [bookCategoryId], references: [id], onDelete: Cascade)
  bookIssues     SmBookIssue[]

  @@map("sm_books")
}

// Library members
model SmLibraryMember {
  id           Int      @id @default(autoincrement())
  studentStaffId Int    @map("student_staff_id")
  memberType   String   @map("member_type") @db.VarChar(200) // student, staff
  membershipNo String?  @map("membership_no") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  bookIssues   SmBookIssue[]

  @@map("sm_library_members")
}

// Book issues
model SmBookIssue {
  id               Int              @id @default(autoincrement())
  bookId           Int              @map("book_id")
  libraryMemberId  Int              @map("library_member_id")
  givenDate        DateTime         @map("given_date") @db.Date
  dueReturnDate    DateTime         @map("due_return_date") @db.Date
  returnDate       DateTime?        @map("return_date") @db.Date
  note             String?          @db.Text
  activeStatus     Int              @default(1) @map("active_status") @db.TinyInt
  createdBy        Int?             @default(1) @map("created_by")
  updatedBy        Int?             @default(1) @map("updated_by")
  schoolId         Int              @default(1) @map("school_id")
  createdAt        DateTime         @default(now()) @map("created_at")
  updatedAt        DateTime         @updatedAt @map("updated_at")

  // Relations
  book             SmBook           @relation(fields: [bookId], references: [id], onDelete: Cascade)
  libraryMember    SmLibraryMember  @relation(fields: [libraryMemberId], references: [id], onDelete: Cascade)

  @@map("sm_book_issues")
}

// Library subjects
model LibrarySubject {
  id           Int      @id @default(autoincrement())
  subjectName  String   @map("subject_name") @db.VarChar(200)
  subjectCode  String?  @map("subject_code") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("library_subjects")
}
