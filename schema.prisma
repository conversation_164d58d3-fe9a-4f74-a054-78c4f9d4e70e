// InfixEdu School Management System - Main Prisma Schema
// This is the main schema file that includes all other schema files

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Include all schema modules
// Note: In actual implementation, you would use Prisma's schema composition
// or merge these files as Prisma doesn't support multiple schema files natively

// Core system tables
include "user.prisma"
include "academic.prisma"
include "student.prisma"
include "staff.prisma"
include "fees.prisma"
include "examination.prisma"
include "library.prisma"
include "inventory.prisma"
include "communication.prisma"
include "system.prisma"

// Note: Since Prisma doesn't natively support multiple schema files,
// in a real implementation you would either:
// 1. Merge all these into a single schema.prisma file
// 2. Use a build process to combine them
// 3. Use Prisma's experimental schema composition features

// For this example, each .prisma file contains the models for that domain
// and can be copied into this main schema.prisma file when needed
