// Staff Management Models
// Based on InfixEdu migration analysis

// Designations
model SmDesignation {
  id           Int      @id @default(autoincrement())
  title        String?  @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  staff        SmStaff[]

  @@map("sm_designations")
}

// HR Departments
model SmHumanDepartment {
  id           Int      @id @default(autoincrement())
  name         String?  @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  staff        SmStaff[]

  @@map("sm_human_departments")
}

// Main staff table
model SmStaff {
  id                    Int      @id @default(autoincrement())
  userId                Int      @map("user_id")
  employeeId            String?  @map("employee_id") @db.VarChar(200)
  firstName             String?  @map("first_name") @db.VarChar(200)
  lastName              String?  @map("last_name") @db.VarChar(200)
  fullName              String?  @map("full_name") @db.VarChar(200)
  fatherName            String?  @map("father_name") @db.VarChar(200)
  motherName            String?  @map("mother_name") @db.VarChar(200)
  email                 String?  @db.VarChar(200)
  staffPhoto            String?  @map("staff_photo") @db.VarChar(200)
  dateOfBirth           DateTime? @map("date_of_birth") @db.Date
  dateOfJoining         DateTime? @map("date_of_joining") @db.Date
  phone                 String?  @db.VarChar(200)
  emergencyMobile       String?  @map("emergency_mobile") @db.VarChar(200)
  maritalStatus         String?  @map("marital_status") @db.VarChar(200)
  currentAddress        String?  @map("current_address") @db.Text
  permanentAddress      String?  @map("permanent_address") @db.Text
  qualification         String?  @db.Text
  experience            String?  @db.Text
  epfNo                 String?  @map("epf_no") @db.VarChar(200)
  basicSalary           Float?   @map("basic_salary") @db.Float
  contractType          String?  @map("contract_type") @db.VarChar(200)
  workShift             String?  @map("work_shift") @db.VarChar(200)
  workLocation          String?  @map("work_location") @db.VarChar(200)
  casualLeave           String?  @map("casual_leave") @db.VarChar(200)
  medicalLeave          String?  @map("medical_leave") @db.VarChar(200)
  maternityLeave        String?  @map("maternity_leave") @db.VarChar(200)
  bankAccountName       String?  @map("bank_account_name") @db.VarChar(200)
  bankAccountNo         String?  @map("bank_account_no") @db.VarChar(200)
  bankName              String?  @map("bank_name") @db.VarChar(200)
  bankBranchName        String?  @map("bank_branch_name") @db.VarChar(200)
  facebookUrl           String?  @map("facebook_url") @db.VarChar(200)
  twitterUrl            String?  @map("twitter_url") @db.VarChar(200)
  linkedinUrl           String?  @map("linkedin_url") @db.VarChar(200)
  instagramUrl          String?  @map("instagram_url") @db.VarChar(200)
  resumeFile            String?  @map("resume_file") @db.VarChar(200)
  joiningLetterFile     String?  @map("joining_letter_file") @db.VarChar(200)
  otherDocumentFile     String?  @map("other_document_file") @db.VarChar(200)
  drivingLicense        String?  @map("driving_license") @db.VarChar(200)
  genderId              Int?     @map("gender_id")
  religionId            Int?     @map("religion_id")
  bloodgroupId          Int?     @map("bloodgroup_id")
  designationId         Int?     @map("designation_id")
  departmentId          Int?     @map("department_id")
  activeStatus          Int      @default(1) @map("active_status") @db.TinyInt
  createdBy             Int?     @default(1) @map("created_by")
  updatedBy             Int?     @default(1) @map("updated_by")
  schoolId              Int      @default(1) @map("school_id")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  // Relations
  designation           SmDesignation? @relation(fields: [designationId], references: [id])
  department            SmHumanDepartment? @relation(fields: [departmentId], references: [id])
  attendances           SmStaffAttendance[]
  leaveRequests         SmLeaveRequest[]
  payrollGenerates      SmHrPayrollGenerate[]

  @@map("sm_staffs")
}

// Staff attendance
model SmStaffAttendance {
  id             Int      @id @default(autoincrement())
  staffId        Int      @map("staff_id")
  attendanceDate DateTime @map("attendance_date") @db.Date
  inTime         String?  @map("in_time") @db.VarChar(200)
  outTime        String?  @map("out_time") @db.VarChar(200)
  attendanceType Int?     @default(1) @map("attendance_type")
  notes          String?  @db.Text
  activeStatus   Int      @default(1) @map("active_status") @db.TinyInt
  createdBy      Int?     @default(1) @map("created_by")
  updatedBy      Int?     @default(1) @map("updated_by")
  schoolId       Int      @default(1) @map("school_id")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  staff          SmStaff  @relation(fields: [staffId], references: [id], onDelete: Cascade)

  @@map("sm_staff_attendences")
}

// Leave types
model SmLeaveType {
  id           Int      @id @default(autoincrement())
  type         String?  @db.VarChar(200)
  totalDays    Int?     @map("total_days")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  leaveDefines SmLeaveDefine[]
  leaveRequests SmLeaveRequest[]

  @@map("sm_leave_types")
}

// Leave definitions
model SmLeaveDefine {
  id           Int         @id @default(autoincrement())
  userId       Int         @map("user_id")
  leaveTypeId  Int         @map("leave_type_id")
  totalDays    Int?        @map("total_days")
  remainingDays Int?       @map("remaining_days")
  extraTaken   Int?        @map("extra_taken")
  year         String?     @db.VarChar(200)
  activeStatus Int         @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?        @default(1) @map("created_by")
  updatedBy    Int?        @default(1) @map("updated_by")
  schoolId     Int         @default(1) @map("school_id")
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")

  // Relations
  leaveType    SmLeaveType @relation(fields: [leaveTypeId], references: [id], onDelete: Cascade)

  @@map("sm_leave_defines")
}

// Leave requests
model SmLeaveRequest {
  id           Int         @id @default(autoincrement())
  staffId      Int         @map("staff_id")
  leaveTypeId  Int         @map("leave_type_id")
  leaveFromDate DateTime   @map("leave_from_date") @db.Date
  leaveToDate  DateTime    @map("leave_to_date") @db.Date
  totalDays    Int?        @map("total_days")
  reason       String?     @db.Text
  fileAttach   String?     @map("file_attach") @db.VarChar(200)
  approveStatus String?    @default("P") @map("approve_status") @db.VarChar(200)
  approvedBy   Int?        @map("approved_by")
  activeStatus Int         @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?        @default(1) @map("created_by")
  updatedBy    Int?        @default(1) @map("updated_by")
  schoolId     Int         @default(1) @map("school_id")
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")

  // Relations
  staff        SmStaff     @relation(fields: [staffId], references: [id], onDelete: Cascade)
  leaveType    SmLeaveType @relation(fields: [leaveTypeId], references: [id], onDelete: Cascade)

  @@map("sm_leave_requests")
}

// Hourly rates
model SmHourlyRate {
  id           Int      @id @default(autoincrement())
  type         String?  @db.VarChar(200)
  amount       Float?   @db.Float
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_hourly_rates")
}

// HR Salary templates
model SmHrSalaryTemplate {
  id           Int      @id @default(autoincrement())
  name         String?  @db.VarChar(200)
  basicSalary  Float?   @map("basic_salary") @db.Float
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_hr_salary_templates")
}

// HR Payroll generation
model SmHrPayrollGenerate {
  id           Int      @id @default(autoincrement())
  staffId      Int      @map("staff_id")
  payrollMonth String?  @map("payroll_month") @db.VarChar(200)
  payrollYear  String?  @map("payroll_year") @db.VarChar(200)
  basicSalary  Float?   @map("basic_salary") @db.Float
  totalEarning Float?   @map("total_earning") @db.Float
  totalDeduction Float? @map("total_deduction") @db.Float
  netSalary    Float?   @map("net_salary") @db.Float
  payrollStatus String? @map("payroll_status") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  staff        SmStaff  @relation(fields: [staffId], references: [id], onDelete: Cascade)
  earnDeducs   SmHrPayrollEarnDeduc[]

  @@map("sm_hr_payroll_generates")
}

// HR Payroll earnings and deductions
model SmHrPayrollEarnDeduc {
  id                 Int                 @id @default(autoincrement())
  payrollGenerateId  Int                 @map("payroll_generate_id")
  particulars        String?             @db.VarChar(200)
  amount             Float?              @db.Float
  type               String?             @db.VarChar(200) // earning or deduction
  activeStatus       Int                 @default(1) @map("active_status") @db.TinyInt
  createdBy          Int?                @default(1) @map("created_by")
  updatedBy          Int?                @default(1) @map("updated_by")
  schoolId           Int                 @default(1) @map("school_id")
  createdAt          DateTime            @default(now()) @map("created_at")
  updatedAt          DateTime            @updatedAt @map("updated_at")

  // Relations
  payrollGenerate    SmHrPayrollGenerate @relation(fields: [payrollGenerateId], references: [id], onDelete: Cascade)

  @@map("sm_hr_payroll_earn_deducs")
}

// Leave deduction information
model SmLeaveDeductionInfo {
  id           Int      @id @default(autoincrement())
  staffId      Int      @map("staff_id")
  leaveMonth   String?  @map("leave_month") @db.VarChar(200)
  leaveYear    String?  @map("leave_year") @db.VarChar(200)
  totalDays    Int?     @map("total_days")
  deductionAmount Float? @map("deduction_amount") @db.Float
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_leave_deduction_infos")
}

// Staff attendance import
model SmStaffAttendanceImport {
  id             Int      @id @default(autoincrement())
  staffId        Int      @map("staff_id")
  attendanceDate DateTime @map("attendance_date") @db.Date
  inTime         String?  @map("in_time") @db.VarChar(200)
  outTime        String?  @map("out_time") @db.VarChar(200)
  attendanceType String?  @map("attendance_type") @db.VarChar(200)
  schoolId       Int      @default(1) @map("school_id")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  @@map("sm_staff_attendance_imports")
}

// Staff bulk import temporaries
model StaffImportBulkTemporary {
  id              Int      @id @default(autoincrement())
  firstName       String?  @map("first_name") @db.VarChar(200)
  lastName        String?  @map("last_name") @db.VarChar(200)
  employeeId      String?  @map("employee_id") @db.VarChar(200)
  email           String?  @db.VarChar(200)
  phone           String?  @db.VarChar(200)
  designationId   Int?     @map("designation_id")
  departmentId    Int?     @map("department_id")
  dateOfJoining   DateTime? @map("date_of_joining") @db.Date
  basicSalary     Float?   @map("basic_salary") @db.Float
  schoolId        Int      @default(1) @map("school_id")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  @@map("staff_import_bulk_temporaries")
}

// Staff registration fields
model SmStaffRegistrationField {
  id           Int      @id @default(autoincrement())
  fieldName    String?  @map("field_name") @db.VarChar(200)
  fieldType    String?  @map("field_type") @db.VarChar(200)
  isRequired   Int      @default(0) @map("is_required") @db.TinyInt
  isVisible    Int      @default(1) @map("is_visible") @db.TinyInt
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_staff_registration_fields")
}

// Payroll payments
model PayrollPayment {
  id           Int      @id @default(autoincrement())
  staffId      Int      @map("staff_id")
  payrollId    Int      @map("payroll_id")
  paymentDate  DateTime @map("payment_date") @db.Date
  amount       Float    @db.Float
  paymentMethod String? @map("payment_method") @db.VarChar(200)
  notes        String?  @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("payroll_payments")
}

// Teacher evaluations
model TeacherEvaluation {
  id           Int      @id @default(autoincrement())
  teacherId    Int      @map("teacher_id")
  evaluatorId  Int      @map("evaluator_id")
  evaluationDate DateTime @map("evaluation_date") @db.Date
  totalScore   Float?   @map("total_score") @db.Float
  grade        String?  @db.VarChar(10)
  comments     String?  @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("teacher_evaluations")
}

// Teacher evaluation settings
model TeacherEvaluationSetting {
  id           Int      @id @default(autoincrement())
  criteriaName String   @map("criteria_name") @db.VarChar(200)
  maxScore     Float    @map("max_score") @db.Float
  weight       Float?   @db.Float
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("teacher_evaluation_settings")
}
