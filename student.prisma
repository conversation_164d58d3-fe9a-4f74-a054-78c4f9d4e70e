// Student Management Models
// Based on InfixEdu migration analysis

// Student categories
model SmStudentCategory {
  id           Int      @id @default(autoincrement())
  categoryName String?  @map("category_name") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  students     SmStudent[]

  @@map("sm_student_categories")
}

// Student groups
model SmStudentGroup {
  id           Int      @id @default(autoincrement())
  group        String?  @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_student_groups")
}

// Parents
model SmParent {
  id                    Int      @id @default(autoincrement())
  userId                Int      @map("user_id")
  fathersName           String?  @map("fathers_name") @db.VarChar(200)
  fathersPhone          String?  @map("fathers_phone") @db.VarChar(200)
  fathersOccupation     String?  @map("fathers_occupation") @db.VarChar(200)
  fathersPhoto          String?  @map("fathers_photo") @db.VarChar(200)
  mothersName           String?  @map("mothers_name") @db.VarChar(200)
  mothersPhone          String?  @map("mothers_phone") @db.VarChar(200)
  mothersOccupation     String?  @map("mothers_occupation") @db.VarChar(200)
  mothersPhoto          String?  @map("mothers_photo") @db.VarChar(200)
  guardiansName         String?  @map("guardians_name") @db.VarChar(200)
  guardiansEmail        String?  @map("guardians_email") @db.VarChar(200)
  guardiansPhone        String?  @map("guardians_phone") @db.VarChar(200)
  guardiansOccupation   String?  @map("guardians_occupation") @db.VarChar(200)
  guardiansRelation     String?  @map("guardians_relation") @db.VarChar(200)
  guardiansPhoto        String?  @map("guardians_photo") @db.VarChar(200)
  guardiansAddress      String?  @map("guardians_address") @db.VarChar(200)
  isGuardian            Int?     @default(0) @map("is_guardian")
  activeStatus          Int      @default(1) @map("active_status") @db.TinyInt
  createdBy             Int?     @default(1) @map("created_by")
  updatedBy             Int?     @default(1) @map("updated_by")
  schoolId              Int      @default(1) @map("school_id")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  // Relations
  students              SmStudent[]

  @@map("sm_parents")
}

// Main students table
model SmStudent {
  id                    Int      @id @default(autoincrement())
  userId                Int      @map("user_id")
  parentId              Int      @map("parent_id")
  rollNo                String?  @map("roll_no") @db.VarChar(200)
  admissionNo           String?  @map("admission_no") @db.VarChar(200)
  admissionDate         DateTime? @map("admission_date") @db.Date
  firstName             String?  @map("first_name") @db.VarChar(200)
  lastName              String?  @map("last_name") @db.VarChar(200)
  fullName              String?  @map("full_name") @db.VarChar(200)
  dateOfBirth           DateTime? @map("date_of_birth") @db.Date
  age                   Int?
  religion              String?  @db.VarChar(200)
  genderId              Int?     @map("gender_id")
  bloodgroupId          Int?     @map("bloodgroup_id")
  studentCategoryId     Int?     @map("student_category_id")
  studentGroupId        Int?     @map("student_group_id")
  height                String?  @db.VarChar(200)
  weight                String?  @db.VarChar(200)
  currentAddress        String?  @map("current_address") @db.Text
  permanentAddress      String?  @map("permanent_address") @db.Text
  nationalIdNo          String?  @map("national_id_no") @db.VarChar(200)
  localIdNo             String?  @map("local_id_no") @db.VarChar(200)
  bankAccountNo         String?  @map("bank_account_no") @db.VarChar(200)
  bankName              String?  @map("bank_name") @db.VarChar(200)
  previousSchoolDetails String?  @map("previous_school_details") @db.Text
  additionalNotes       String?  @map("additional_notes") @db.Text
  dormitoryId           Int?     @map("dormitory_id")
  roomId                Int?     @map("room_id")
  transportId           Int?     @map("transport_id")
  vehicleId             Int?     @map("vehicle_id")
  driverId              Int?     @map("driver_id")
  studentPhoto          String?  @map("student_photo") @db.VarChar(200)
  activeStatus          Int      @default(1) @map("active_status") @db.TinyInt
  createdBy             Int?     @default(1) @map("created_by")
  updatedBy             Int?     @default(1) @map("updated_by")
  schoolId              Int      @default(1) @map("school_id")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  // Relations
  school                SmSchool @relation(fields: [schoolId], references: [id], onDelete: Cascade)
  parent                SmParent @relation(fields: [parentId], references: [id], onDelete: Cascade)
  category              SmStudentCategory? @relation(fields: [studentCategoryId], references: [id])
  studentRecords        StudentRecord[]
  attendances           SmStudentAttendance[]
  promotions            SmStudentPromotion[]
  documents             SmStudentDocument[]
  timelines             SmStudentTimeline[]
  certificates          SmStudentCertificate[]
  idCards               SmStudentIdCard[]

  @@map("sm_students")
}

// Student records (academic enrollment)
model StudentRecord {
  id           Int            @id @default(autoincrement())
  studentId    Int            @map("student_id")
  classId      Int            @map("class_id")
  sectionId    Int            @map("section_id")
  sessionId    Int            @map("session_id")
  academicId   Int            @map("academic_id")
  rollNo       String?        @map("roll_no") @db.VarChar(200)
  isPromote    Int            @default(0) @map("is_promote")
  isDefault    Int            @default(0) @map("is_default")
  isGraduate   Int            @default(0) @map("is_graduate")
  activeStatus Int            @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?           @default(1) @map("created_by")
  updatedBy    Int?           @default(1) @map("updated_by")
  schoolId     Int            @default(1) @map("school_id")
  createdAt    DateTime       @default(now()) @map("created_at")
  updatedAt    DateTime       @updatedAt @map("updated_at")

  // Relations
  student      SmStudent      @relation(fields: [studentId], references: [id], onDelete: Cascade)
  academicYear SmAcademicYear @relation(fields: [academicId], references: [id], onDelete: Cascade)

  @@map("student_records")
}

// Student attendance
model SmStudentAttendance {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  attendanceDate DateTime @map("attendance_date") @db.Date
  attendanceType String   @map("attendance_type") @db.VarChar(10)
  notes        String?  @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  student      SmStudent @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("sm_student_attendances")
}

// Student promotions
model SmStudentPromotion {
  id              Int      @id @default(autoincrement())
  studentId       Int      @map("student_id")
  previousClassId Int      @map("previous_class_id")
  previousSectionId Int    @map("previous_section_id")
  currentClassId  Int      @map("current_class_id")
  currentSectionId Int     @map("current_section_id")
  previousSessionId Int    @map("previous_session_id")
  currentSessionId Int     @map("current_session_id")
  previousRollId  String?  @map("previous_roll_id") @db.VarChar(200)
  currentRollId   String?  @map("current_roll_id") @db.VarChar(200)
  promotionDate   DateTime @map("promotion_date") @db.Date
  activeStatus    Int      @default(1) @map("active_status") @db.TinyInt
  createdBy       Int?     @default(1) @map("created_by")
  updatedBy       Int?     @default(1) @map("updated_by")
  schoolId        Int      @default(1) @map("school_id")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  student         SmStudent @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("sm_student_promotions")
}

// Student documents
model SmStudentDocument {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  title        String?  @db.VarChar(200)
  documentFile String?  @map("document_file") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  student      SmStudent @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("sm_student_documents")
}

// Student timeline
model SmStudentTimeline {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  staffId      Int?     @map("staff_id")
  title        String?  @db.VarChar(200)
  date         DateTime? @db.Date
  description  String?  @db.Text
  file         String?  @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  student      SmStudent @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("sm_student_timelines")
}

// Student certificates
model SmStudentCertificate {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  certificateName String? @map("certificate_name") @db.VarChar(200)
  headerLeftText String? @map("header_left_text") @db.VarChar(500)
  date         DateTime? @db.Date
  body         String?  @db.Text
  bodyTwo      String?  @map("body_two") @db.Text
  certificateNo String? @map("certificate_no") @db.Text
  type         String?  @default("school") @db.VarChar(255)
  studentPhoto String?  @map("student_photo") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  student      SmStudent @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("sm_student_certificates")
}

// Student ID cards
model SmStudentIdCard {
  id                  Int      @id @default(autoincrement())
  studentId           Int      @map("student_id")
  title               String?  @db.VarChar(30)
  pageLayoutStyle     String?  @map("page_layout_style") @db.VarChar(30)
  userPhotoStyle      String?  @map("user_photo_style") @db.VarChar(30)
  userPhotoWidth      String?  @map("user_photo_width") @db.VarChar(30)
  admissionNo         String?  @map("admission_no") @db.VarChar(10)
  studentName         String?  @map("student_name") @db.VarChar(10)
  class               String?  @db.VarChar(10)
  fatherName          String?  @map("father_name") @db.VarChar(10)
  motherName          String?  @map("mother_name") @db.VarChar(10)
  studentAddress      String?  @map("student_address") @db.VarChar(10)
  phoneNumber         String?  @map("phone_number") @db.VarChar(10)
  blood               String?  @db.VarChar(10)
  dob                 String?  @db.VarChar(10)
  photo               String?  @db.VarChar(255)
  signature           String?  @db.VarChar(255)
  staffDepartment     String?  @map("staff_department") @db.VarChar(255)
  staffDesignation    String?  @map("staff_designation") @db.VarChar(255)
  activeStatus        Int      @default(1) @map("active_status") @db.TinyInt
  createdBy           Int?     @default(1) @map("created_by")
  updatedBy           Int?     @default(1) @map("updated_by")
  schoolId            Int      @default(1) @map("school_id")
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  // Relations
  student             SmStudent @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("sm_student_id_cards")
}

// Student bulk import temporaries
model StudentBulkTemporary {
  id           Int      @id @default(autoincrement())
  firstName    String?  @map("first_name") @db.VarChar(200)
  lastName     String?  @map("last_name") @db.VarChar(200)
  admissionNo  String?  @map("admission_no") @db.VarChar(200)
  rollNo       String?  @map("roll_no") @db.VarChar(200)
  classId      Int?     @map("class_id")
  sectionId    Int?     @map("section_id")
  genderId     Int?     @map("gender_id")
  dateOfBirth  DateTime? @map("date_of_birth") @db.Date
  phoneNumber  String?  @map("phone_number") @db.VarChar(200)
  email        String?  @db.VarChar(200)
  password     String?  @db.VarChar(200)
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("student_bulk_temporaries")
}

// Student attendance bulk import
model StudentAttendanceBulk {
  id              Int      @id @default(autoincrement())
  studentId       Int      @map("student_id")
  studentRecordId Int?     @map("student_record_id")
  classId         Int      @map("class_id")
  sectionId       Int      @map("section_id")
  attendanceDate  DateTime @map("attendance_date") @db.Date
  attendanceType  String   @map("attendance_type") @db.VarChar(10)
  schoolId        Int      @default(1) @map("school_id")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  @@map("student_attendance_bulks")
}

// Student academic history
model StudentAcademicHistory {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  academicYear String   @map("academic_year") @db.VarChar(200)
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  rollNo       String?  @map("roll_no") @db.VarChar(200)
  result       String?  @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("student_academic_histories")
}

// Graduates
model Graduate {
  id           Int      @id @default(autoincrement())
  studentId    Int      @map("student_id")
  academicId   Int      @map("academic_id")
  sessionId    Int      @map("session_id")
  classId      Int      @map("class_id")
  sectionId    Int      @map("section_id")
  graduateDate DateTime @map("graduate_date") @db.Date
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("graduates")
}
