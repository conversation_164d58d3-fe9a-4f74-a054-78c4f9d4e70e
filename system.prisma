// System Configuration Models
// Based on InfixEdu migration analysis

// Base groups
model SmBaseGroup {
  id           Int      @id @default(autoincrement())
  name         String?  @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  baseSetups   SmBaseSetup[]

  @@map("sm_base_groups")
}

// Base setups
model SmBaseSetup {
  id             Int         @id @default(autoincrement())
  baseGroupId    Int         @map("base_group_id")
  baseSetupName  String?     @map("base_setup_name") @db.VarChar(200)
  activeStatus   Int         @default(1) @map("active_status") @db.TinyInt
  createdBy      Int?        @default(1) @map("created_by")
  updatedBy      Int?        @default(1) @map("updated_by")
  schoolId       Int         @default(1) @map("school_id")
  createdAt      DateTime    @default(now()) @map("created_at")
  updatedAt      DateTime    @updatedAt @map("updated_at")

  // Relations
  baseGroup      SmBaseGroup @relation(fields: [baseGroupId], references: [id], onDelete: Cascade)

  @@map("sm_base_setups")
}

// General settings
model SmGeneralSetting {
  id                  Int      @id @default(autoincrement())
  schoolName          String?  @map("school_name") @db.VarChar(200)
  siteTitle           String?  @map("site_title") @db.VarChar(200)
  schoolCode          String?  @map("school_code") @db.VarChar(200)
  address             String?  @db.Text
  phone               String?  @db.VarChar(200)
  email               String?  @db.VarChar(200)
  emailSignature      String?  @map("email_signature") @db.Text
  currency            String?  @db.VarChar(200)
  currencySymbol      String?  @map("currency_symbol") @db.VarChar(200)
  timezone            String?  @db.VarChar(200)
  dateFormat          String?  @map("date_format") @db.VarChar(200)
  timeFormat          String?  @map("time_format") @db.VarChar(200)
  weekStartDay        String?  @map("week_start_day") @db.VarChar(200)
  academicYear        Int?     @map("academic_year")
  sessionId           Int?     @map("session_id")
  languageId          Int?     @map("language_id")
  dateFormatId        Int?     @map("date_format_id")
  weekendId           Int?     @map("weekend_id")
  logo                String?  @db.VarChar(200)
  favicon             String?  @db.VarChar(200)
  copyrightText       String?  @map("copyright_text") @db.Text
  fileSize            Int?     @map("file_size")
  studentIdFormat     String?  @map("student_id_format") @db.VarChar(200)
  staffIdFormat       String?  @map("staff_id_format") @db.VarChar(200)
  feesDueReminder     Int?     @map("fees_due_reminder")
  attendanceReminder  Int?     @map("attendance_reminder")
  examReminder        Int?     @map("exam_reminder")
  roleBasedSidebar    Boolean  @default(false) @map("role_based_sidebar")
  carryForwordDueDay  Int      @default(60) @map("carry_forword_due_day")
  shiftEnable         Int?     @default(0) @map("shift_enable") @db.TinyInt
  activeStatus        Int      @default(1) @map("active_status") @db.TinyInt
  createdBy           Int?     @default(1) @map("created_by")
  updatedBy           Int?     @default(1) @map("updated_by")
  schoolId            Int      @default(1) @map("school_id")
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  @@map("sm_general_settings")
}

// Modules
model SmModule {
  id           Int      @id @default(autoincrement())
  name         String?  @db.VarChar(200)
  details      String?  @db.Text
  status       Int?     @default(1)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_modules")
}

// Module links
model SmModuleLink {
  id           Int      @id @default(autoincrement())
  name         String?  @db.VarChar(200)
  route        String?  @db.VarChar(200)
  moduleId     Int?     @map("module_id")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_module_links")
}

// Module permissions
model SmModulePermission {
  id           Int      @id @default(autoincrement())
  name         String?  @db.VarChar(200)
  route        String?  @db.VarChar(200)
  moduleId     Int?     @map("module_id")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_module_permissions")
}

// Module permission assigns
model SmModulePermissionAssign {
  id                 Int      @id @default(autoincrement())
  modulePermissionId Int      @map("module_permission_id")
  roleId             Int      @map("role_id")
  activeStatus       Int      @default(1) @map("active_status") @db.TinyInt
  createdBy          Int?     @default(1) @map("created_by")
  updatedBy          Int?     @default(1) @map("updated_by")
  schoolId           Int      @default(1) @map("school_id")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  @@map("sm_module_permission_assigns")
}

// Languages
model SmLanguage {
  id           Int      @id @default(autoincrement())
  language     String?  @db.VarChar(200)
  nativeName   String?  @map("native_name") @db.VarChar(200)
  universalName String? @map("universal_name") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_languages")
}

// Language phrases
model SmLanguagePhrase {
  id           Int      @id @default(autoincrement())
  phrase       String?  @db.VarChar(200)
  en           String?  @db.Text
  es           String?  @db.Text
  fr           String?  @db.Text
  ar           String?  @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_language_phrases")
}

// Date formats
model SmDateFormat {
  id           Int      @id @default(autoincrement())
  format       String?  @db.VarChar(255)
  normalView   String?  @map("normal_view") @db.VarChar(255)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_date_formats")
}

// Time zones
model SmTimeZone {
  id           Int      @id @default(autoincrement())
  timeZone     String?  @map("time_zone") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_time_zones")
}

// Currencies
model SmCurrency {
  id           Int      @id @default(autoincrement())
  name         String?  @db.VarChar(200)
  code         String?  @db.VarChar(200)
  symbol       String?  @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_currencies")
}

// Styles
model SmStyle {
  id           Int      @id @default(autoincrement())
  styleName    String?  @map("style_name") @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_styles")
}

// Colors
model Color {
  id           Int      @id @default(autoincrement())
  name         String   @db.VarChar(255)
  colorCode    String   @map("color_code") @db.VarChar(255)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  colorThemes  ColorTheme[]

  @@map("colors")
}

// Themes
model Theme {
  id           Int      @id @default(autoincrement())
  title        String   @db.VarChar(255)
  path         String   @db.VarChar(255)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  colorThemes  ColorTheme[]

  @@map("themes")
}

// Color theme relationships
model ColorTheme {
  id       Int   @id @default(autoincrement())
  colorId  Int   @map("color_id")
  themeId  Int   @map("theme_id")
  schoolId Int   @default(1) @map("school_id")

  // Relations
  color    Color @relation(fields: [colorId], references: [id], onDelete: Cascade)
  theme    Theme @relation(fields: [themeId], references: [id], onDelete: Cascade)

  @@map("color_theme")
}

// System versions
model SmSystemVersion {
  id           Int      @id @default(autoincrement())
  versionName  String   @map("version_name") @db.VarChar(255)
  title        String   @db.VarChar(255)
  features     String   @db.VarChar(255)
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_system_versions")
}

// Version histories
model VersionHistory {
  id           Int      @id @default(autoincrement())
  version      String   @db.VarChar(255)
  releaseDate  DateTime @map("release_date") @db.Date
  features     String?  @db.Text
  bugFixes     String?  @map("bug_fixes") @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("version_histories")
}

// Backups
model SmBackup {
  id           Int      @id @default(autoincrement())
  fileName     String?  @map("file_name") @db.VarChar(255)
  sourceLink   String?  @map("source_link") @db.VarChar(255)
  fileType     Int?     @map("file_type") @db.TinyInt // 0=Database, 1=File, 2=Image
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  langType     Int?     @map("lang_type")
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_backups")
}

// Setup admins
model SmSetupAdmin {
  id           Int      @id @default(autoincrement())
  type         String?  @db.VarChar(200)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_setup_admins")
}

// Infix module managers
model InfixModuleManager {
  id           Int      @id @default(autoincrement())
  name         String   @db.VarChar(255)
  email        String?  @db.VarChar(255)
  notes        String?  @db.Text
  version      String?  @db.VarChar(255)
  updateUrl    String?  @map("update_url") @db.VarChar(255)
  installed    Int      @default(0) @db.TinyInt
  activated    Int      @default(0) @db.TinyInt
  langType     Int?     @map("lang_type")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    Int?     @default(1) @map("created_by")
  updatedBy    Int?     @default(1) @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("infix_module_managers")
}

// School modules
model SchoolModule {
  id           Int      @id @default(autoincrement())
  moduleName   String   @map("module_name") @db.VarChar(255)
  isEnabled    Int      @default(1) @map("is_enabled") @db.TinyInt
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("school_modules")
}

// Maintenance settings
model MaintenanceSetting {
  id           Int      @id @default(autoincrement())
  maintenanceTitle String @map("maintenance_title") @db.VarChar(255)
  maintenanceSubTitle String @map("maintenance_sub_title") @db.VarChar(255)
  maintenanceMode Int   @default(0) @map("maintenance_mode") @db.TinyInt
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("maintenance_settings")
}

// Plugins
model Plugin {
  id           Int      @id @default(autoincrement())
  name         String   @db.VarChar(255)
  version      String   @db.VarChar(255)
  description  String?  @db.Text
  isActive     Int      @default(0) @map("is_active") @db.TinyInt
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("plugins")
}

// Add-ons
model SmAddOn {
  id        BigInt   @id @default(autoincrement())
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("sm_add_ons")
}

// Custom fields
model SmCustomField {
  id           Int      @id @default(autoincrement())
  fieldName    String   @map("field_name") @db.VarChar(255)
  fieldType    String   @map("field_type") @db.VarChar(255)
  fieldFor     String   @map("field_for") @db.VarChar(255) // student, staff, etc.
  isRequired   Int      @default(0) @map("is_required") @db.TinyInt
  isVisible    Int      @default(1) @map("is_visible") @db.TinyInt
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_custom_fields")
}

// Dashboard settings
model SmDashboardSetting {
  id           Int      @id @default(autoincrement())
  settingName  String   @map("setting_name") @db.VarChar(255)
  settingValue String?  @map("setting_value") @db.Text
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_dashboard_settings")
}

// Background settings
model SmBackgroundSetting {
  id           Int      @id @default(autoincrement())
  title        String   @db.VarChar(255)
  backgroundImage String? @map("background_image") @db.VarChar(255)
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("sm_background_settings")
}
