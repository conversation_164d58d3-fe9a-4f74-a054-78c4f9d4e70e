// User and Authentication Models
// Based on InfixEdu migration analysis

// Main schools table - multi-tenancy support
model SmSchool {
  id               Int      @id @default(autoincrement())
  schoolName       String?  @map("school_name") @db.VarChar(200)
  createdBy        Int      @default(1) @map("created_by") @db.TinyInt
  updatedBy        Int      @default(1) @map("updated_by") @db.TinyInt
  email            String?  @db.VarChar(200)
  domain           String   @default("school") @db.VarChar(191)
  address          String?  @db.Text
  phone            String?  @db.VarChar(20)
  schoolCode       String?  @map("school_code") @db.VarChar(200)
  isEmailVerified  Boolean  @default(false) @map("is_email_verified")
  startingDate     DateTime? @map("starting_date") @db.Date
  endingDate       DateTime? @map("ending_date") @db.Date
  packageId        Int?     @map("package_id")
  planType         String?  @map("plan_type") @db.VarChar(200)
  region           Int?
  contactType      ContactType? @map("contact_type")
  activeStatus     Int      @default(1) @map("active_status") @db.TinyInt
  isEnabled        String   @default("yes") @map("is_enabled") @db.VarChar(20)
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Relations
  users            User[]
  roles            Role[]
  academicYears    SmAcademicYear[]
  classes          SmClass[]
  students         SmStudent[]
  staff            SmStaff[]

  @@map("sm_schools")
}

enum ContactType {
  yearly
  monthly
  once
}

// User roles
model Role {
  id           Int      @id @default(autoincrement())
  name         String?  @db.VarChar(100)
  type         String   @default("System")
  activeStatus Int      @default(1) @map("active_status") @db.TinyInt
  createdBy    String?  @default("1") @map("created_by")
  updatedBy    String?  @default("1") @map("updated_by")
  schoolId     Int      @default(1) @map("school_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  school       SmSchool @relation(fields: [schoolId], references: [id], onDelete: Cascade)
  users        User[]
  rolePermissions SmRolePermission[]

  @@map("roles")
}

// Main users table
model User {
  id                Int      @id @default(autoincrement())
  fullName          String?  @map("full_name") @db.VarChar(192)
  username          String?  @db.VarChar(192)
  phoneNumber       String?  @map("phone_number") @db.VarChar(191)
  email             String?  @db.VarChar(192)
  password          String?  @db.VarChar(100)
  usertype          String?  @db.VarChar(210)
  activeStatus      Int      @default(1) @map("active_status") @db.TinyInt
  randomCode        String?  @map("random_code") @db.Text
  notificationToken String?  @map("notificationToken") @db.Text
  rememberToken     String?  @map("remember_token") @db.VarChar(100)
  language          String?  @default("en")
  styleId           Int?     @default(1) @map("style_id")
  rtlLtl            Int?     @default(2) @map("rtl_ltl")
  selectedSession   Int?     @default(1) @map("selected_session")
  createdBy         Int?     @default(1) @map("created_by")
  updatedBy         Int?     @default(1) @map("updated_by")
  accessStatus      Int?     @default(1) @map("access_status")
  schoolId          Int      @default(1) @map("school_id")
  roleId            Int?     @map("role_id")
  isAdministrator   IsAdministrator @default(no) @map("is_administrator")
  isRegistered      Int      @default(0) @map("is_registered") @db.TinyInt
  deviceToken       String?  @map("device_token") @db.Text
  stripeId          String?  @map("stripe_id")
  cardBrand         String?  @map("card_brand")
  cardLastFour      String?  @map("card_last_four") @db.VarChar(4)
  verified          String?
  trialEndsAt       DateTime? @map("trial_ends_at")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // Relations
  school            SmSchool @relation(fields: [schoolId], references: [id], onDelete: Cascade)
  role              Role?    @relation(fields: [roleId], references: [id], onDelete: Cascade)
  userLogs          SmUserLog[]

  @@map("users")
}

enum IsAdministrator {
  yes
  no
}

// Password reset tokens
model PasswordReset {
  email     String   @db.VarChar(191)
  token     String   @db.VarChar(191)
  createdAt DateTime @default(now()) @map("created_at")

  @@map("password_resets")
  @@index([email])
}

// OAuth tables for API authentication
model OauthAuthCode {
  id        String    @id @db.VarChar(100)
  userId    BigInt    @map("user_id")
  clientId  BigInt    @map("client_id")
  scopes    String?   @db.Text
  revoked   Boolean
  expiresAt DateTime? @map("expires_at")

  @@map("oauth_auth_codes")
  @@index([userId])
}

model OauthAccessToken {
  id        String    @id @db.VarChar(100)
  userId    BigInt?   @map("user_id")
  clientId  BigInt    @map("client_id")
  name      String?
  scopes    String?   @db.Text
  revoked   Boolean
  createdAt DateTime? @map("created_at")
  updatedAt DateTime? @map("updated_at")
  expiresAt DateTime? @map("expires_at")

  @@map("oauth_access_tokens")
  @@index([userId])
}

model OauthRefreshToken {
  id              String    @id @db.VarChar(100)
  accessTokenId   String    @map("access_token_id") @db.VarChar(100)
  revoked         Boolean
  expiresAt       DateTime? @map("expires_at")

  @@map("oauth_refresh_tokens")
  @@index([accessTokenId])
}

model OauthClient {
  id                     BigInt  @id @default(autoincrement())
  userId                 BigInt? @map("user_id")
  name                   String
  secret                 String? @db.VarChar(100)
  provider               String?
  redirect               String  @db.Text
  personalAccessClient  Boolean @map("personal_access_client")
  passwordClient        Boolean @map("password_client")
  revoked               Boolean
  createdAt             DateTime? @map("created_at")
  updatedAt             DateTime? @map("updated_at")

  @@map("oauth_clients")
  @@index([userId])
}

model OauthPersonalAccessClient {
  id        BigInt    @id @default(autoincrement())
  clientId  BigInt    @map("client_id")
  createdAt DateTime? @map("created_at")
  updatedAt DateTime? @map("updated_at")

  @@map("oauth_personal_access_clients")
}

// User activity logs
model SmUserLog {
  id        Int      @id @default(autoincrement())
  userId    Int      @map("user_id")
  ipAddress String?  @map("ip_address") @db.VarChar(45)
  userAgent String?  @map("user_agent") @db.Text
  action    String?  @db.VarChar(255)
  details   String?  @db.Text
  schoolId  Int      @default(1) @map("school_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sm_user_logs")
}

// Role permissions
model SmRolePermission {
  id       Int  @id @default(autoincrement())
  roleId   Int  @map("role_id")
  moduleId Int  @map("module_id")
  schoolId Int  @default(1) @map("school_id")

  // Relations
  role     Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@map("sm_role_permissions")
}
